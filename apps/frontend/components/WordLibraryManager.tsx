/**
 * 词库管理主组件
 * 🎯 核心价值：统一的词库管理界面，支持29个词库的可视化管理
 * 📦 功能范围：词库列表、折叠展开、颜色分类、词语管理
 * 🔄 架构设计：基于状态驱动的响应式组件，支持实时更新
 */

'use client';

import Button from '@/components/ui/Button';
import WordInput from '@/components/ui/WordInput';
import type {
  BasicColorType,
  DataLevel,
  WordLibraryKey
} from '@/core/matrix/MatrixTypes';
import {
  AVAILABLE_WORD_LIBRARIES,
  getWordLibraryBackgroundColor,
  getWordLibraryDisplayName,
  getWordLibraryTextColor
} from '@/core/wordLibrary/WordLibraryCore';
import { useWordInputStore, useWordLibraryStore } from '@/core/wordLibrary/WordLibraryStore';
import React, { memo, useCallback, useEffect, useMemo, useRef } from 'react';

// ===== 组件属性 =====

interface WordLibraryManagerProps {
  /** 自定义类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 是否为颜色词语模式（可选，用于外部控制） */
  isColorWordMode?: boolean;
}

// ===== 折叠按钮组件 =====

interface CollapseButtonProps {
  collapsed: boolean;
  onClick: () => void;
  color: BasicColorType;
}

const CollapseButton: React.FC<CollapseButtonProps> = memo(({ collapsed, onClick, color }) => {
  const backgroundColor = useMemo(() => getWordLibraryBackgroundColor(color), [color]);
  const textColor = useMemo(() => getWordLibraryTextColor(backgroundColor), [backgroundColor]);

  return (
    <button
      onClick={onClick}
      className="w-6 h-6 rounded-full border flex items-center justify-center text-xs font-bold mr-2 flex-shrink-0 transition-all duration-200 hover:scale-110"
      style={{
        backgroundColor: collapsed ? backgroundColor : 'transparent',
        color: collapsed ? textColor : backgroundColor,
        borderColor: backgroundColor,
        borderWidth: collapsed ? '1px' : '2px'
      }}
      title={collapsed ? '展开词库' : '折叠词库'}
    >
      {collapsed ? '●' : '○'}
    </button>
  );
};

// ===== 词库项组件 =====

interface WordLibraryItemProps {
  color: BasicColorType;
  level: DataLevel;
  libraryKey: WordLibraryKey;
}

const WordLibraryItem: React.FC<WordLibraryItemProps> = ({ color, level, libraryKey }) => {
  const itemRef = useRef<HTMLDivElement>(null);
  const {
    getLibrary,
    toggleLibraryCollapse
  } = useWordLibraryStore();

  // 获取填词模式状态
  const { isActive: isWordInputActive, matchedLibrary } = useWordInputStore();

  const library = getLibrary(libraryKey);
  const displayName = getWordLibraryDisplayName(color, level);
  const backgroundColor = getWordLibraryBackgroundColor(color);

  // 检查是否为当前使用的词库 - 使用useMemo避免频繁重计算
  const isActiveLibrary = useMemo(() => {
    return isWordInputActive && matchedLibrary === libraryKey;
  }, [isWordInputActive, matchedLibrary, libraryKey]);

  const handleToggleCollapse = useCallback(() => {
    toggleLibraryCollapse(libraryKey);
  }, [libraryKey, toggleLibraryCollapse]);

  // 优化的滑动逻辑 - 使用防抖和更温和的滑动方式
  const hasScrolledRef = useRef(false);
  const lastActiveLibraryRef = useRef<string | null>(null);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 防抖的滑动函数
  const debouncedScrollIntoView = useCallback(() => {
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    scrollTimeoutRef.current = setTimeout(() => {
      if (itemRef.current) {
        // 使用更温和的滑动方式，减少视觉冲击
        const element = itemRef.current;
        const container = element.closest('.overflow-y-auto');

        if (container) {
          const containerRect = container.getBoundingClientRect();
          const elementRect = element.getBoundingClientRect();

          // 检查元素是否已经在可视区域内
          const isVisible = elementRect.top >= containerRect.top &&
            elementRect.bottom <= containerRect.bottom;

          if (!isVisible) {
            // 使用更平滑的滚动，减少闪烁
            element.scrollIntoView({
              behavior: 'smooth',
              block: 'nearest', // 使用nearest而不是center，减少滚动距离
              inline: 'nearest'
            });
          }
        }
      }
    }, 150); // 150ms防抖延迟
  }, []);

  // 优化的自动滑动逻辑 - 防抖版本
  useEffect(() => {
    // 如果不是激活状态，重置滑动标记
    if (!isActiveLibrary) {
      hasScrolledRef.current = false;
      lastActiveLibraryRef.current = null;
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
        scrollTimeoutRef.current = null;
      }
      return;
    }

    // 如果没有元素引用，跳过
    if (!itemRef.current) {
      return;
    }

    // 检查是否是新的激活状态（不同的词库被激活）
    const currentLibraryKey = matchedLibrary;
    const isNewActivation = lastActiveLibraryRef.current !== currentLibraryKey;

    // 如果不是新的激活状态且已经滑动过，跳过
    if (!isNewActivation && hasScrolledRef.current) {
      return;
    }

    // 如果是新的激活状态，执行防抖滑动
    if (isNewActivation) {
      console.log('执行词库滑动到可视区域:', libraryKey);
      debouncedScrollIntoView();

      // 标记已滑动
      hasScrolledRef.current = true;
      lastActiveLibraryRef.current = currentLibraryKey;
    }
  }, [isActiveLibrary, matchedLibrary, libraryKey, debouncedScrollIntoView]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  if (!library) return null;

  return (
    <div ref={itemRef} className="mb-3" data-word-library={libraryKey}>
      {/* 词库标题栏 */}
      <div className="flex items-center mb-2">
        <CollapseButton
          collapsed={library.collapsed}
          onClick={handleToggleCollapse}
          color={color}
        />
        <div
          className={`flex-1 px-3 py-1 rounded-md text-sm font-medium ${isActiveLibrary ? 'word-library-active' : ''}`}
          style={{
            backgroundColor: backgroundColor + '20',
            color: backgroundColor,
            border: `1px solid ${backgroundColor}40`
          }}
        >
          {/* 富文本格式显示 */}
          <span>
            【{displayName}[{library.words.length}词]：
            {library.words.slice(0, 3).map((word, index) => (
              <span key={word.id}>
                {word.text}[{word.usagePositions?.length || 0}]
                {index < Math.min(library.words.length - 1, 2) ? '，' : ''}
              </span>
            ))}
            {library.words.length > 3 && '…'}
            】
          </span>
        </div>
      </div>

      {/* 词库输入框 */}
      <div className="ml-8">
        <WordInput
          libraryKey={libraryKey}
          color={color}
          level={level}
          collapsed={library.collapsed}
          placeholder={`输入${displayName}词语...`}
        />
      </div>
    </div>
  );
};



// ===== 主组件 =====

const WordLibraryManagerComponent: React.FC<WordLibraryManagerProps> = memo(({
  className = '',
  style,
  isColorWordMode = true // 默认为true，保持向后兼容
}) => {
  const { resetAllLibraries, exportData, importData } = useWordLibraryStore();

  // 如果不是【颜色】【词语】模式，显示提示
  if (!isColorWordMode) {
    return (
      <div className={`word-library-manager ${className} flex flex-col h-full items-center justify-center`} style={style}>
        <div className="text-center text-gray-500">
          <p className="text-sm">词库管理功能仅在【颜色】【词语】模式下可用</p>
          <p className="text-xs mt-1">请切换到颜色模式 + 词语内容模式</p>
        </div>
      </div>
    );
  }

  // 处理导出
  const handleExport = useCallback(() => {
    const data = exportData();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `词库数据_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [exportData]);

  // 处理导入
  const handleImport = useCallback(() => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const data = e.target?.result as string;
          const success = importData(data);
          if (success) {
            alert('导入成功！');
          } else {
            alert('导入失败，请检查文件格式。');
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  }, [importData]);

  // 处理重置
  const handleReset = useCallback(() => {
    if (confirm('确定要清空所有词库吗？此操作不可撤销。')) {
      resetAllLibraries();
    }
  }, [resetAllLibraries]);

  return (
    <div className={`word-library-manager ${className} flex flex-col h-full`} style={style}>
      {/* 标题栏 */}
      <div className="flex items-center justify-between mb-4 flex-shrink-0">
        <h3 className="text-lg font-semibold text-gray-800">词库管理</h3>
        <div className="flex space-x-2">
          <Button
            variant="secondary"
            size="sm"
            onClick={handleExport}
            title="导出词库数据"
          >
            导出
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={handleImport}
            title="导入词库数据"
          >
            导入
          </Button>
          <Button
            variant="danger"
            size="sm"
            onClick={handleReset}
            title="清空所有词库"
          >
            清空
          </Button>
        </div>
      </div>

      {/* 词库列表 - 可滚动容器 */}
      <div className="flex-1 overflow-y-auto space-y-1 pr-2">
        {AVAILABLE_WORD_LIBRARIES.map(({ color, level }) => {
          const libraryKey = `${color}-${level}` as WordLibraryKey;
          return (
            <WordLibraryItem
              key={libraryKey}
              color={color}
              level={level}
              libraryKey={libraryKey}
            />
          );
        })}
      </div>
    </div>
  );
});

// ===== 性能优化 =====

const WordLibraryManager = memo(WordLibraryManagerComponent);

WordLibraryManager.displayName = 'WordLibraryManager';

export default WordLibraryManager;
export type { WordLibraryManagerProps };
