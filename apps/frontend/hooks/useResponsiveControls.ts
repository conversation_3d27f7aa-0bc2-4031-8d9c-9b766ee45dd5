/**
 * 响应式控制面板Hook
 * 🎯 核心价值：智能的响应式控制面板显示逻辑
 * 📦 功能范围：窗口尺寸监听、自动显示/隐藏控制面板
 * 🔄 架构设计：基于窗口尺寸的自动化UI控制
 */

'use client';

import { AUTO_HIDE_BREAKPOINT } from '@/core/matrix/MatrixConfig';
import { useCallback, useEffect, useRef, useState } from 'react';

// ===== Hook接口 =====

interface UseResponsiveControlsReturn {
  /** 当前窗口宽度 */
  windowWidth: number;
  /** 当前窗口高度 */
  windowHeight: number;
  /** 是否应该悬浮显示控制面板 */
  shouldFloat: boolean;
  /** 是否为移动设备 */
  isMobile: boolean;
  /** 是否为平板设备 */
  isTablet: boolean;
  /** 是否为桌面设备 */
  isDesktop: boolean;
  /** 控制面板是否可见 */
  controlsVisible: boolean;
  /** 设置控制面板可见性 */
  setControlsVisible: (visible: boolean) => void;
  /** 切换控制面板可见性 */
  toggleControls: () => void;
  /** 控制面板显示模式 */
  displayMode: 'normal' | 'floating' | 'hidden';
}

// ===== 主Hook =====

export const useResponsiveControls = (): UseResponsiveControlsReturn => {
  // 简化的状态管理 - 合并相关状态
  const [state, setState] = useState({
    windowWidth: 0,
    windowHeight: 0,
    controlsVisible: true,
    userControlled: false,
    isClient: false
  });

  // 使用统一的响应式配置

  // 简化的防抖机制
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastWindowWidthRef = useRef<number>(0);

  // 确保客户端渲染
  useEffect(() => {
    setState(prev => ({ ...prev, isClient: true }));
  }, []);

  // 窗口尺寸监听
  useEffect(() => {
    if (!state.isClient) return;

    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;

      setState(prev => ({
        ...prev,
        windowWidth: width,
        windowHeight: height
      }));
    };

    // 初始化
    handleResize();

    // 添加监听器
    window.addEventListener('resize', handleResize);

    // 清理
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [state.isClient]);

  // 修复的用户操作处理函数
  const handleUserAction = useCallback((action: () => void) => {
    // 清除之前的防抖定时器
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // 标记为用户控制并执行操作
    setState(prev => ({ ...prev, userControlled: true }));
    action();

    // 1000ms后允许自动响应式调整（延长防抖时间）
    debounceTimeoutRef.current = setTimeout(() => {
      setState(prev => ({ ...prev, userControlled: false }));
    }, 100);
  }, []);

  // 修复的响应式控制面板可见性管理 - 增加防护条件
  useEffect(() => {
    if (state.windowWidth === 0) return; // 避免初始化时的误触发

    const isSmallWindow = state.windowWidth < AUTO_HIDE_BREAKPOINT;
    const lastWidth = lastWindowWidthRef.current;
    const wasSmallWindow = lastWidth < AUTO_HIDE_BREAKPOINT;

    // 检查是否跨越了断点
    const crossedBreakpoint = (isSmallWindow !== wasSmallWindow) && lastWidth > 0;

    // 更新上次窗口宽度
    lastWindowWidthRef.current = state.windowWidth;

    // 增加防护条件：避免在交互过程中误触发
    // 1. 只有在跨越断点且没有用户控制时才进行自动调整
    // 2. 增加宽度变化阈值，避免微小变化触发
    const significantChange = Math.abs(state.windowWidth - lastWidth) > 50;

    if (crossedBreakpoint && !state.userControlled && significantChange) {
      // 使用setTimeout延迟执行，避免在快速交互过程中触发
      const timeoutId = setTimeout(() => {
        setState(prev => {
          // 再次检查状态，确保在延迟期间没有用户操作
          if (prev.userControlled) return prev;

          const currentIsSmall = window.innerWidth < AUTO_HIDE_BREAKPOINT;
          if (currentIsSmall && prev.controlsVisible) {
            // 窗口变小，自动隐藏控制面板
            return { ...prev, controlsVisible: false };
          } else if (!currentIsSmall && !prev.controlsVisible) {
            // 窗口变大，自动显示控制面板
            return { ...prev, controlsVisible: true };
          }
          return prev;
        });
      }, 100); // 100ms延迟，避免快速变化

      return () => clearTimeout(timeoutId);
    }
  }, [state.windowWidth]);

  // 计算响应式状态
  const shouldFloat = state.windowWidth > 0 && state.windowWidth < AUTO_HIDE_BREAKPOINT;
  const isMobile = state.windowWidth > 0 && state.windowWidth < 768;
  const isTablet = state.windowWidth >= 768 && state.windowWidth < 1024;
  const isDesktop = state.windowWidth >= 1024;

  // 计算显示模式
  const displayMode: 'normal' | 'floating' | 'hidden' =
    shouldFloat ? (state.controlsVisible ? 'floating' : 'hidden') :
      (state.controlsVisible ? 'normal' : 'hidden');

  // 切换控制面板可见性
  const toggleControls = useCallback(() => {
    handleUserAction(() => {
      setState(prev => ({ ...prev, controlsVisible: !prev.controlsVisible }));
    });
  }, [handleUserAction]);

  // 设置控制面板可见性（包装原始setter以标记用户控制）
  const setControlsVisibleWithUserControl = useCallback((visible: boolean) => {
    handleUserAction(() => {
      setState(prev => ({ ...prev, controlsVisible: visible }));
    });
  }, [handleUserAction]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  return {
    windowWidth: state.windowWidth,
    windowHeight: state.windowHeight,
    shouldFloat,
    isMobile,
    isTablet,
    isDesktop,
    controlsVisible: state.controlsVisible,
    setControlsVisible: setControlsVisibleWithUserControl,
    toggleControls,
    displayMode,
  };
};

// ===== 类型导出 =====

export type { UseResponsiveControlsReturn };

